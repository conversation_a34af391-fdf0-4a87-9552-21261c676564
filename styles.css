/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.header h1 i {
    color: #3498db;
    margin-right: 15px;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.1em;
    font-weight: 300;
}

/* 会议信息区域 */
.meeting-info {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #3498db;
}

/* AI设置区域 */
.ai-settings-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    border-left: 4px solid #9b59b6;
}

.ai-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.ai-settings-header h3 {
    color: #2c3e50;
    font-size: 1.2em;
    display: flex;
    align-items: center;
    margin: 0;
}

.ai-settings-header h3 i {
    margin-right: 10px;
    color: #9b59b6;
}

.ai-settings-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.ai-config-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.config-label {
    min-width: 120px;
    font-weight: 500;
    color: #2c3e50;
}

.config-input,
.config-select {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.config-input:focus,
.config-select:focus {
    outline: none;
    border-color: #9b59b6;
    box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.password-input-group {
    display: flex;
    flex: 1;
    gap: 5px;
}

.password-input-group input {
    flex: 1;
}

.btn-sm {
    padding: 8px 12px;
    font-size: 12px;
}

/* 高级参数区域 */
.ai-config-advanced {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.advanced-params {
    margin-top: 15px;
    padding: 15px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.param-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
}

.param-row label {
    min-width: 140px;
    font-size: 13px;
    color: #6c757d;
}

.param-slider {
    flex: 1;
    margin: 0 10px;
}

.param-input {
    width: 100px;
    padding: 6px 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 13px;
}

/* 连接状态 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 15px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-unknown {
    background: #6c757d;
}

.status-connected {
    background: #28a745;
    animation: pulse-green 2s infinite;
}

.status-error {
    background: #dc3545;
    animation: pulse-red 2s infinite;
}

.status-testing {
    background: #ffc107;
    animation: pulse-yellow 1s infinite;
}

@keyframes pulse-green {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes pulse-red {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes pulse-yellow {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 13px;
    color: #6c757d;
}

.info-row {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr;
    gap: 15px;
}

.input-field {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 语音识别区域 */
.voice-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

/* 音频源选择区域 */
.audio-source-selection {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #e74c3c;
}

.audio-source-selection h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
}

.audio-source-selection h4 i {
    margin-right: 10px;
    color: #e74c3c;
}

.audio-source-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.audio-source-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.audio-source-option:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.audio-source-option input[type="radio"] {
    margin-bottom: 8px;
    transform: scale(1.2);
}

.audio-source-option input[type="radio"]:checked + i {
    color: #3498db;
}

.audio-source-option input[type="radio"]:checked ~ span {
    color: #3498db;
    font-weight: 600;
}

.audio-source-option input[type="radio"]:checked {
    accent-color: #3498db;
}

.audio-source-option.selected {
    border-color: #3498db;
    background: #f0f8ff;
}

.audio-source-option i {
    font-size: 1.5em;
    margin-bottom: 8px;
    color: #6c757d;
    transition: color 0.3s ease;
}

.audio-source-option span {
    font-weight: 500;
    margin-bottom: 5px;
    color: #2c3e50;
}

.audio-source-option small {
    color: #6c757d;
    font-size: 0.85em;
    line-height: 1.3;
}

.audio-source-help {
    text-align: center;
}

.voice-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.transcription-area h3,
.manual-input-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
    display: flex;
    align-items: center;
}

.transcription-area h3 i,
.manual-input-section h3 i {
    margin-right: 10px;
    color: #3498db;
}

.text-display {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.placeholder-text {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    margin-top: 80px;
}

/* 手动输入区域 */
.manual-input-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.manual-textarea {
    width: 100%;
    min-height: 250px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.manual-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

/* 状态指示器 */
.status-indicator {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    background: #e9ecef;
    color: #6c757d;
    margin-left: 10px;
}

.status-indicator.recording {
    background: #dc3545;
    color: white;
    animation: pulse 1.5s infinite;
}

.status-indicator.paused {
    background: #ffc107;
    color: #212529;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 控制按钮组 */
.transcription-controls,
.input-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 会议纪要区域 */
.summary-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.summary-header h3 {
    color: #2c3e50;
    font-size: 1.3em;
    display: flex;
    align-items: center;
}

.summary-header h3 i {
    margin-right: 10px;
    color: #27ae60;
}

.summary-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.summary-mode-select {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 150px;
}

.summary-mode-select:focus {
    outline: none;
    border-color: #27ae60;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

/* 自定义提示词区域 */
.custom-prompt-area {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.prompt-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #2c3e50;
}

.custom-prompt-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

.custom-prompt-textarea:focus {
    outline: none;
    border-color: #17a2b8;
    box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.1);
}

/* 生成进度指示器 */
.generation-progress {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #9b59b6);
    border-radius: 4px;
    animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
    font-style: italic;
}

.summary-display {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    font-size: 14px;
    line-height: 1.8;
}

/* 操作按钮区域 */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

/* 历史记录区域 */
.history-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.modal-large {
    max-width: 800px;
    margin: 5% auto;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h3 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
}

.modal-header h3 i {
    margin-right: 10px;
    color: #3498db;
}

.modal-body {
    line-height: 1.6;
}

.help-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.help-section h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.help-section h4 i {
    margin-right: 10px;
    color: #3498db;
}

.help-section p {
    margin-bottom: 10px;
    color: #495057;
}

.help-section ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.help-section li {
    margin-bottom: 5px;
    color: #6c757d;
}

.help-steps {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.help-steps h5 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.help-steps ol {
    margin-left: 20px;
}

.help-steps li {
    margin-bottom: 8px;
    color: #495057;
}

.help-tips {
    border-left-color: #f39c12;
}

.tip-item {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.tip-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 8px;
}

.tip-item p {
    margin-bottom: 8px;
}

.tip-item ul {
    margin-left: 20px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-row {
        grid-template-columns: 1fr;
    }

    .voice-controls {
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .summary-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    /* AI设置响应式 */
    .ai-settings-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .ai-config-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .config-label {
        min-width: auto;
        margin-bottom: 5px;
    }

    .password-input-group {
        flex-direction: row;
    }

    .param-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .param-row label {
        min-width: auto;
    }

    .summary-controls {
        flex-direction: column;
        gap: 10px;
    }

    .summary-mode-select {
        min-width: auto;
        width: 100%;
    }
}
