<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jason's Meeting Note - 智能会议记录</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <h1><i class="fas fa-microphone"></i> Jason's Meeting Note</h1>
            <p class="subtitle">智能会议记录与纪要生成工具</p>
        </header>

        <!-- 会议信息设置 -->
        <section class="meeting-info">
            <div class="info-row">
                <input type="text" id="meetingTitle" placeholder="会议主题" class="input-field">
                <input type="datetime-local" id="meetingTime" class="input-field">
                <input type="text" id="participants" placeholder="参会人员（用逗号分隔）" class="input-field">
            </div>
        </section>

        <!-- AI设置区域 -->
        <section class="ai-settings-section">
            <div class="ai-settings-header">
                <h3><i class="fas fa-robot"></i> AI智能助手设置</h3>
                <button id="toggleAiSettings" class="btn btn-outline">
                    <i class="fas fa-cog"></i> 配置AI
                </button>
            </div>

            <div id="aiSettingsPanel" class="ai-settings-panel" style="display: none;">
                <!-- API选择 -->
                <div class="ai-config-row">
                    <label for="apiProvider" class="config-label">选择AI服务商：</label>
                    <select id="apiProvider" class="config-select">
                        <option value="">请选择AI服务商</option>
                        <option value="deepseek">DeepSeek API</option>
                        <option value="minimax">MiniMax API</option>
                        <option value="doubao">豆包(字节跳动) API</option>
                        <option value="gemini">Google Gemini API</option>
                        <option value="openai">OpenAI GPT API</option>
                        <option value="custom">自定义API</option>
                    </select>
                </div>

                <!-- API配置区域 -->
                <div id="apiConfigArea" class="api-config-area" style="display: none;">
                    <!-- API Key -->
                    <div class="ai-config-row">
                        <label for="apiKey" class="config-label">API Key：</label>
                        <div class="password-input-group">
                            <input type="password" id="apiKey" placeholder="请输入您的API Key" class="config-input">
                            <button type="button" id="toggleApiKey" class="btn btn-outline btn-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- API Base URL -->
                    <div class="ai-config-row">
                        <label for="apiBaseUrl" class="config-label">API Base URL：</label>
                        <input type="url" id="apiBaseUrl" placeholder="API基础URL" class="config-input">
                    </div>

                    <!-- 模型选择 -->
                    <div class="ai-config-row">
                        <label for="modelName" class="config-label">模型名称：</label>
                        <select id="modelName" class="config-select">
                            <option value="">请先选择API服务商</option>
                        </select>
                    </div>

                    <!-- 高级参数 -->
                    <div class="ai-config-advanced">
                        <button id="toggleAdvanced" class="btn btn-outline btn-sm">
                            <i class="fas fa-sliders-h"></i> 高级参数
                        </button>
                        <div id="advancedParams" class="advanced-params" style="display: none;">
                            <div class="param-row">
                                <label for="temperature">Temperature (0-2):</label>
                                <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="param-slider">
                                <span id="temperatureValue">0.7</span>
                            </div>
                            <div class="param-row">
                                <label for="maxTokens">Max Tokens:</label>
                                <input type="number" id="maxTokens" min="100" max="4000" value="2000" class="param-input">
                            </div>
                        </div>
                    </div>

                    <!-- 连接测试 -->
                    <div class="ai-config-row">
                        <button id="testConnection" class="btn btn-info">
                            <i class="fas fa-plug"></i> 测试连接
                        </button>
                        <div id="connectionStatus" class="connection-status">
                            <span class="status-dot status-unknown"></span>
                            <span class="status-text">未测试</span>
                        </div>
                    </div>

                    <!-- 保存配置 -->
                    <div class="ai-config-row">
                        <button id="saveAiConfig" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存配置
                        </button>
                        <button id="clearAiConfig" class="btn btn-warning">
                            <i class="fas fa-trash"></i> 清空配置
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主要功能区域 -->
        <main class="main-content">
            <!-- 语音识别控制区 -->
            <section class="voice-section">
                <!-- 音频源选择 -->
                <div class="audio-source-selection">
                    <h4><i class="fas fa-audio-description"></i> 音频源设置</h4>
                    <div class="audio-source-options">
                        <label class="audio-source-option">
                            <input type="radio" name="audioSource" value="microphone" checked>
                            <i class="fas fa-microphone"></i>
                            <span>麦克风输入</span>
                            <small>捕获麦克风声音</small>
                        </label>
                        <label class="audio-source-option">
                            <input type="radio" name="audioSource" value="screen">
                            <i class="fas fa-desktop"></i>
                            <span>系统音频</span>
                            <small>捕获系统声音（需要屏幕共享权限）</small>
                        </label>
                        <label class="audio-source-option">
                            <input type="radio" name="audioSource" value="both">
                            <i class="fas fa-volume-up"></i>
                            <span>混合音频</span>
                            <small>同时捕获麦克风和系统声音</small>
                        </label>
                    </div>
                    <div class="audio-source-help">
                        <button id="showAudioHelp" class="btn btn-outline btn-sm">
                            <i class="fas fa-question-circle"></i> 音频设置帮助
                        </button>
                    </div>
                </div>

                <div class="voice-controls">
                    <button id="startRecording" class="btn btn-primary">
                        <i class="fas fa-microphone"></i> 开始录音
                    </button>
                    <button id="stopRecording" class="btn btn-secondary" disabled>
                        <i class="fas fa-stop"></i> 停止录音
                    </button>
                    <button id="pauseRecording" class="btn btn-warning" disabled>
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <span id="recordingStatus" class="status-indicator">准备就绪</span>
                </div>
                
                <!-- 语音转录显示区 -->
                <div class="transcription-area">
                    <h3><i class="fas fa-volume-up"></i> 语音转录内容</h3>
                    <div id="transcriptionText" class="text-display">
                        <p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>
                    </div>
                    <div class="transcription-controls">
                        <button id="clearTranscription" class="btn btn-outline">
                            <i class="fas fa-trash"></i> 清空转录
                        </button>
                    </div>
                </div>
            </section>

            <!-- 手动输入区 -->
            <section class="manual-input-section">
                <h3><i class="fas fa-edit"></i> 个人笔记与关键点</h3>
                <textarea id="manualNotes" placeholder="在此输入您的个人想法、关键词、重要观点等...&#10;&#10;提示：&#10;• 记录重要决策和行动项&#10;• 标注关键人员和时间点&#10;• 补充语音识别可能遗漏的信息" class="manual-textarea"></textarea>
                <div class="input-controls">
                    <button id="clearManualNotes" class="btn btn-outline">
                        <i class="fas fa-eraser"></i> 清空笔记
                    </button>
                    <button id="addTimestamp" class="btn btn-outline">
                        <i class="fas fa-clock"></i> 插入时间戳
                    </button>
                </div>
            </section>
        </main>

        <!-- 会议纪要生成区 -->
        <section class="summary-section">
            <div class="summary-header">
                <h3><i class="fas fa-file-alt"></i> 智能会议纪要</h3>
                <div class="summary-controls">
                    <select id="summaryMode" class="summary-mode-select">
                        <option value="basic">基础整合</option>
                        <option value="ai-concise">AI简洁版</option>
                        <option value="ai-detailed">AI详细版</option>
                        <option value="ai-action">AI行动项提取</option>
                        <option value="ai-custom">AI自定义</option>
                    </select>
                    <button id="generateSummary" class="btn btn-success">
                        <i class="fas fa-magic"></i> 生成纪要
                    </button>
                </div>
            </div>

            <!-- AI自定义提示词区域 -->
            <div id="customPromptArea" class="custom-prompt-area" style="display: none;">
                <label for="customPrompt" class="prompt-label">自定义AI提示词：</label>
                <textarea id="customPrompt" placeholder="请输入您希望AI如何处理会议内容的指令...&#10;&#10;例如：&#10;• 请总结会议的主要决策和行动项&#10;• 提取关键讨论点和结论&#10;• 按照议题分类整理内容" class="custom-prompt-textarea"></textarea>
            </div>

            <!-- 生成进度指示器 -->
            <div id="generationProgress" class="generation-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p class="progress-text">AI正在分析会议内容，请稍候...</p>
            </div>

            <div id="meetingSummary" class="summary-display">
                <p class="placeholder-text">选择生成模式并点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>
            </div>
        </section>

        <!-- 操作按钮区 -->
        <section class="action-buttons">
            <button id="saveMeeting" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存会议记录
            </button>
            <button id="exportMeeting" class="btn btn-info">
                <i class="fas fa-download"></i> 导出为文档
            </button>
            <button id="newMeeting" class="btn btn-warning">
                <i class="fas fa-plus"></i> 新建会议
            </button>
            <button id="loadMeeting" class="btn btn-secondary">
                <i class="fas fa-folder-open"></i> 加载历史记录
            </button>
        </section>

        <!-- 历史记录区 -->
        <section class="history-section" style="display: none;">
            <h3><i class="fas fa-history"></i> 历史会议记录</h3>
            <div id="historyList" class="history-list">
                <!-- 历史记录将在这里动态加载 -->
            </div>
        </section>
    </div>

    <!-- 状态提示模态框 -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalMessage"></div>
        </div>
    </div>

    <!-- 音频设置帮助模态框 -->
    <div id="audioHelpModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close">&times;</span>
            <div class="modal-header">
                <h3><i class="fas fa-question-circle"></i> 音频捕获设置指南</h3>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h4><i class="fas fa-microphone"></i> 麦克风输入</h4>
                    <p>标准模式，捕获您的麦克风声音。适用于：</p>
                    <ul>
                        <li>个人语音记录</li>
                        <li>面对面会议记录</li>
                        <li>电话会议（通过扬声器播放）</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4><i class="fas fa-desktop"></i> 系统音频捕获</h4>
                    <p>捕获系统音频输出，可以录制其他应用的声音。适用于：</p>
                    <ul>
                        <li>腾讯会议、Zoom、Teams等视频会议</li>
                        <li>在线课程、网络研讨会</li>
                        <li>音频播放内容</li>
                    </ul>
                    <div class="help-steps">
                        <h5>使用步骤：</h5>
                        <ol>
                            <li>选择"系统音频"选项</li>
                            <li>点击"开始录音"</li>
                            <li>浏览器会请求屏幕共享权限</li>
                            <li>选择要共享的窗口或整个屏幕</li>
                            <li>确保勾选"共享音频"选项</li>
                        </ol>
                    </div>
                </div>

                <div class="help-section">
                    <h4><i class="fas fa-volume-up"></i> 混合音频</h4>
                    <p>同时捕获麦克风和系统音频，适用于：</p>
                    <ul>
                        <li>需要记录自己发言和他人发言的会议</li>
                        <li>互动式在线课程</li>
                        <li>需要完整记录的重要会议</li>
                    </ul>
                </div>

                <div class="help-section help-tips">
                    <h4><i class="fas fa-lightbulb"></i> 使用技巧</h4>
                    <div class="tip-item">
                        <strong>macOS用户：</strong>
                        <p>如果系统音频捕获不工作，可以尝试安装虚拟音频设备：</p>
                        <ul>
                            <li>下载并安装 BlackHole 或 SoundFlower</li>
                            <li>在系统偏好设置中创建多输出设备</li>
                            <li>将会议应用音频输出到虚拟设备</li>
                        </ul>
                    </div>
                    <div class="tip-item">
                        <strong>Windows用户：</strong>
                        <p>可以使用"立体声混音"功能：</p>
                        <ul>
                            <li>右键点击音量图标 → 声音设置</li>
                            <li>选择"声音控制面板"</li>
                            <li>在录制选项卡中启用"立体声混音"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="ai-button-fix-final.js"></script>

    <!-- 内联调试脚本 -->
    <script>
        // 页面加载完成后的额外检查
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('🔍 页面完全加载后的AI按钮检查...');

                const button = document.getElementById('toggleAiSettings');
                const panel = document.getElementById('aiSettingsPanel');

                if (button && panel && window.meetingApp) {
                    console.log('✅ 所有必要元素都已就绪');

                    // 如果按钮仍然无响应，提供手动修复提示
                    setTimeout(function() {
                        console.log('💡 如果AI按钮仍无响应，请在控制台运行: fixAIButtonFinal()');
                    }, 2000);
                } else {
                    console.warn('⚠️ 某些元素可能未正确加载:');
                    console.log('  - 按钮:', !!button);
                    console.log('  - 面板:', !!panel);
                    console.log('  - 应用实例:', !!window.meetingApp);
                }
            }, 1000);
        });
    </script>
</body>
</html>
